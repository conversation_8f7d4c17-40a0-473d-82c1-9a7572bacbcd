# LiquidBounce Ver_zhzh 自动构建测试脚本
# PowerShell版本 - 功能更强大

param(
    [string]$MinecraftDir = "F:\QQDownload\minecraft",
    [switch]$SkipBuild,
    [switch]$SkipLaunch,
    [switch]$CleanOnly
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 颜色输出函数
function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    Write-Host $Text -ForegroundColor $Color
}

function Write-Step {
    param([string]$Step, [string]$Description)
    Write-ColorText "[$Step] $Description" "Cyan"
}

function Write-Success {
    param([string]$Message)
    Write-ColorText "✅ $Message" "Green"
}

function Write-Error {
    param([string]$Message)
    Write-ColorText "❌ $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorText "⚠️ $Message" "Yellow"
}

# 主函数
function Main {
    Write-ColorText "========================================" "Magenta"
    Write-ColorText "   LiquidBounce Ver_zhzh 自动构建测试" "Magenta"
    Write-ColorText "========================================" "Magenta"
    Write-Host ""

    # 设置变量
    $ProjectDir = Get-Location
    $ModsDir = Join-Path $MinecraftDir "mods"
    $BuildDir = Join-Path $ProjectDir "build\libs"

    Write-ColorText "📍 项目目录: $ProjectDir" "Gray"
    Write-ColorText "📍 Minecraft目录: $MinecraftDir" "Gray"
    Write-ColorText "📍 Mods目录: $ModsDir" "Gray"
    Write-Host ""

    try {
        # 步骤1: 清理
        if ($CleanOnly) {
            Write-Step "1/1" "仅清理构建文件..."
            Invoke-GradleTask "clean"
            Write-Success "清理完成！"
            return
        }

        if (-not $SkipBuild) {
            Write-Step "1/4" "清理旧的构建文件..."
            Invoke-GradleTask "clean"
            Write-Success "清理完成"

            Write-Host ""
            Write-Step "2/4" "开始构建项目..."
            Invoke-GradleTask "build"
            Write-Success "构建完成"
        } else {
            Write-Warning "跳过构建步骤"
        }

        Write-Host ""
        Write-Step "3/4" "部署到Minecraft mods文件夹..."
        Deploy-ToMods $ModsDir $BuildDir
        Write-Success "部署完成"

        if (-not $SkipLaunch) {
            Write-Host ""
            Write-Step "4/4" "启动Minecraft进行测试..."
            Launch-Minecraft $MinecraftDir
        } else {
            Write-Warning "跳过启动步骤"
        }

        Write-Host ""
        Write-Success "所有步骤完成！"
        
    } catch {
        Write-Error "执行失败: $($_.Exception.Message)"
        exit 1
    }
}

# Gradle任务执行
function Invoke-GradleTask {
    param([string]$Task)
    
    $process = Start-Process -FilePath ".\gradlew.bat" -ArgumentList $Task -Wait -PassThru -NoNewWindow
    if ($process.ExitCode -ne 0) {
        throw "Gradle任务 '$Task' 执行失败 (退出码: $($process.ExitCode))"
    }
}

# 部署到mods文件夹
function Deploy-ToMods {
    param([string]$ModsDir, [string]$BuildDir)
    
    # 创建mods目录
    if (-not (Test-Path $ModsDir)) {
        Write-ColorText "📁 创建mods目录: $ModsDir" "Yellow"
        New-Item -ItemType Directory -Path $ModsDir -Force | Out-Null
    }

    # 删除旧的LiquidBounce mod文件
    Write-ColorText "🗑️ 清理旧的mod文件..." "Yellow"
    Get-ChildItem -Path $ModsDir -Filter "*liquidbounce*.jar" | ForEach-Object {
        Write-ColorText "   删除: $($_.Name)" "Gray"
        Remove-Item $_.FullName -Force
    }

    # 复制新构建的jar文件
    Write-ColorText "📦 复制新的mod文件..." "Yellow"
    Get-ChildItem -Path $BuildDir -Filter "*.jar" | Where-Object { 
        $_.Name -notlike "*-sources.jar" -and $_.Name -notlike "*-dev.jar"
    } | ForEach-Object {
        Write-ColorText "   复制: $($_.Name) → $ModsDir" "Gray"
        Copy-Item $_.FullName -Destination $ModsDir -Force
    }
}

# 启动Minecraft
function Launch-Minecraft {
    param([string]$MinecraftDir)
    
    Write-ColorText "🚀 正在启动Minecraft客户端..." "Green"
    Write-Host ""
    Write-Warning "注意事项："
    Write-ColorText "   - 请在游戏中检查窗口标题是否显示 'LiquidBounce Legacy ... - Ver_zhzh Edition'" "Gray"
    Write-ColorText "   - 检查mod列表中是否显示 'LiquidBounce Ver_zhzh'" "Gray"
    Write-ColorText "   - 测试完成后请关闭游戏" "Gray"
    Write-Host ""

    # 尝试找到并启动Minecraft
    $launchers = @("minecraft.exe", "launcher.exe", "MinecraftLauncher.exe")
    $launched = $false
    
    foreach ($launcher in $launchers) {
        $launcherPath = Join-Path $MinecraftDir $launcher
        if (Test-Path $launcherPath) {
            Write-ColorText "🎮 启动: $launcher" "Green"
            Start-Process -FilePath $launcherPath -WorkingDirectory $MinecraftDir
            $launched = $true
            break
        }
    }
    
    if (-not $launched) {
        Write-Warning "未找到Minecraft启动器，请手动启动Minecraft"
        Write-ColorText "📂 正在打开Minecraft目录..." "Yellow"
        Start-Process -FilePath "explorer.exe" -ArgumentList $MinecraftDir
    }
}

# 显示帮助
function Show-Help {
    Write-ColorText "LiquidBounce Ver_zhzh 自动构建测试脚本" "Cyan"
    Write-Host ""
    Write-ColorText "用法:" "White"
    Write-ColorText "  .\build-and-test.ps1 [参数]" "Gray"
    Write-Host ""
    Write-ColorText "参数:" "White"
    Write-ColorText "  -MinecraftDir <路径>  指定Minecraft目录 (默认: F:\QQDownload\minecraft)" "Gray"
    Write-ColorText "  -SkipBuild           跳过构建步骤，仅部署现有文件" "Gray"
    Write-ColorText "  -SkipLaunch          跳过启动Minecraft" "Gray"
    Write-ColorText "  -CleanOnly           仅执行清理操作" "Gray"
    Write-Host ""
    Write-ColorText "示例:" "White"
    Write-ColorText "  .\build-and-test.ps1                                    # 完整流程" "Gray"
    Write-ColorText "  .\build-and-test.ps1 -SkipBuild                         # 跳过构建" "Gray"
    Write-ColorText "  .\build-and-test.ps1 -MinecraftDir 'D:\Games\Minecraft' # 自定义目录" "Gray"
}

# 检查是否请求帮助
if ($args -contains "-h" -or $args -contains "--help" -or $args -contains "/?") {
    Show-Help
    return
}

# 执行主函数
Main
