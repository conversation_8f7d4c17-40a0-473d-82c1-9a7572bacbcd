@echo off
chcp 65001 >nul
echo 🚀 LiquidBounce Ver_zhzh 快速测试
echo.

:: 使用Gradle任务进行构建和部署
echo 📦 正在构建并部署到mods文件夹...
call gradlew deployToCustomMods -PminecraftDir="F:/QQDownload/minecraft"

if %ERRORLEVEL% neq 0 (
    echo ❌ 构建或部署失败！
    pause
    exit /b 1
)

echo.
echo ✅ 构建和部署完成！
echo 🎮 现在可以启动Minecraft进行测试了
echo.
echo 💡 提示：
echo    - 检查窗口标题：LiquidBounce Legacy ... - Ver_zhzh Edition
echo    - 检查mod列表：LiquidBounce Ver_zhzh
echo.
pause
