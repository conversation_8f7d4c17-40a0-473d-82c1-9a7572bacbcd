plugins {
    id "java"
    id "idea"
    id "org.jetbrains.kotlin.jvm"
    id "com.github.johnrengelman.shadow" version "6.1.0"
    id "net.minecraftforge.gradle.forge"
    id "org.spongepowered.mixin"
    id "com.gorylenko.gradle-git-properties" version "2.4.2"
}

repositories {
    mavenLocal()
    mavenCentral()
    maven { url = "https://repo.spongepowered.org/repository/maven-public/" }
    maven { url = "https://jitpack.io/" }
}

archivesBaseName = project.archives_base_name
version = project.mod_version
group = project.maven_group

sourceCompatibility = targetCompatibility = 1.8
compileJava.options.encoding = "UTF-8"

compileKotlin {
    kotlinOptions {
        jvmTarget = "1.8"
    }
}

minecraft {
    version = "1.8.9-11.15.1.2318-1.8.9"
    runDir = "run"
    mappings = "stable_22"
    makeObfSourceJar = false
    clientJvmArgs += ["-Dfml.coreMods.load=net.ccbluex.liquidbounce.injection.forge.MixinLoader", "-Xmx4096m", "-Xms1024m", "-Ddev-mode"]
}

configurations {
    runtimeOnly.canBeResolved = true

    // 排除有问题的现代Java模块
    all {
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib-jdk7'
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib-jdk8'
        resolutionStrategy {
            force 'org.jetbrains.kotlin:kotlin-stdlib:1.6.21'
            force 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.1'
        }
    }
}

dependencies {
    implementation("org.spongepowered:mixin:0.7.11-SNAPSHOT") {
        transitive = false
        exclude module: "guava"
        exclude module: "commons-io"
        exclude module: "gson"
        exclude module: "launchwrapper"
        exclude module: "log4j-core"
        exclude module: "slf4j-api"
    }

    annotationProcessor("org.spongepowered:mixin:0.7.11-SNAPSHOT")

    implementation "com.jagrosh:DiscordIPC:0.4"

    implementation("com.github.CCBlueX:Elixir:1.2.6") {
        exclude module: "kotlin-stdlib"
        exclude module: "authlib"
    }

    implementation 'com.github.TheAltening:TheAltening4j:d0771f42d3'
    implementation 'com.github.TheAltening:API-Java-AuthLib:63a9702615'

    // 暂时移除可能有兼容性问题的图表库
    // implementation("org.knowm.xchart:xchart:3.8.8")

    // HTTP Client - 使用兼容版本
    implementation("com.squareup.okhttp3:okhttp:4.9.3") {
        exclude module: "kotlin-stdlib"
        exclude module: "kotlin-stdlib-jdk8"
        exclude module: "kotlin-stdlib-jdk7"
    }

    // Kotlin - 最小化依赖，仅使用核心库
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    // 暂时移除coroutines以避免兼容性问题
    // implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlin_coroutines_version")

    // Swing LookAndFeel
    implementation "com.formdev:flatlaf:3.5.4"

    implementation fileTree(include: ["*.jar"], dir: "libs")
}

shadowJar {
    archiveClassifier.set("")
    duplicatesStrategy DuplicatesStrategy.EXCLUDE

    exclude "LICENSE.txt"

    exclude "META-INF/maven/**"
    exclude "META-INF/versions/**"

    exclude "org/apache/log4j/**"
    exclude "org/apache/commons/**"
    exclude "org/junit/**"
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    filesMatching("mcmod.info") {
        expand "version": project.version, "mcversion": project.minecraft.version
    }

    rename "(.+_at.cfg)", "META-INF/\$1"
}

task moveResources {
    doLast {
        copy {
            from "${buildDir}/resources/main"
            into "${buildDir}/classes/java"
        }
    }
}

moveResources.dependsOn(processResources)
classes.dependsOn(moveResources)

jar {
    manifest.attributes(
            "FMLCorePlugin": "net.ccbluex.liquidbounce.injection.forge.MixinLoader",
            "FMLCorePluginContainsFMLMod": true,
            "ForceLoadAsMod": true,
            "MixinConfigs": "liquidbounce.forge.mixins.json",
            "ModSide": "CLIENT",
            "TweakClass": "org.spongepowered.asm.launch.MixinTweaker",
            "TweakOrder": "0",
            "FMLAT": "liquidbounce_at.cfg",
            "Main-Class": "net.ccbluex.liquidinstruction.LiquidInstructionKt",
    )

    enabled = false
}

mixin {
    disableRefMapWarning = true
    defaultObfuscationEnv searge
    add sourceSets.main, "liquidbounce.mixins.refmap.json"
}

reobf {
    shadowJar {
        mappingType = "SEARGE"
    }
}

jar.dependsOn("shadowJar")

tasks.reobfShadowJar.mustRunAfter shadowJar

task copyZipInclude(type: Copy) {
    from 'zip_include/'
    into 'build/libs/zip'
}

build.dependsOn copyZipInclude

// 自动部署到Minecraft mods文件夹的任务
task deployToMods(type: Copy) {
    description = 'Deploy built jar to Minecraft mods folder'
    group = 'deployment'

    dependsOn build

    from 'build/libs'
    into System.getProperty('user.home') + '/.minecraft/mods'
    include '*.jar'
    exclude '*-sources.jar'
    exclude '*-dev.jar'

    doFirst {
        // 清理旧的LiquidBounce mod文件
        def modsDir = new File(System.getProperty('user.home') + '/.minecraft/mods')
        if (modsDir.exists()) {
            modsDir.listFiles().findAll {
                it.name.toLowerCase().contains('liquidbounce') && it.name.endsWith('.jar')
            }.each {
                println "删除旧文件: ${it.name}"
                it.delete()
            }
        }
    }

    doLast {
        println "✅ 部署完成！Mod已复制到: ${destinationDir}"
    }
}

// 自定义Minecraft目录的部署任务
task deployToCustomMods {
    description = 'Deploy built jar to custom Minecraft mods folder'
    group = 'deployment'

    dependsOn build

    doLast {
        def customMinecraftDir = project.findProperty('minecraftDir') ?: 'F:/QQDownload/minecraft'
        def modsDir = new File(customMinecraftDir, 'mods')

        // 创建mods目录
        if (!modsDir.exists()) {
            modsDir.mkdirs()
            println "📁 创建目录: ${modsDir.absolutePath}"
        }

        // 清理旧文件
        modsDir.listFiles().findAll {
            it.name.toLowerCase().contains('liquidbounce') && it.name.endsWith('.jar')
        }.each {
            println "🗑️ 删除旧文件: ${it.name}"
            it.delete()
        }

        // 复制新文件
        new File('build/libs').listFiles().findAll {
            it.name.endsWith('.jar') &&
            !it.name.contains('-sources') &&
            !it.name.contains('-dev')
        }.each { jarFile ->
            def targetFile = new File(modsDir, jarFile.name)
            jarFile.withInputStream { input ->
                targetFile.withOutputStream { output ->
                    output << input
                }
            }
            println "📦 复制: ${jarFile.name} → ${modsDir.absolutePath}"
        }

        println "✅ 部署完成！"
        println "📍 目标目录: ${modsDir.absolutePath}"
    }
}

// IDEA启动前的快速部署任务
task ideaDeploy {
    description = 'Quick deploy for IDEA run configuration'
    group = 'ide'

    dependsOn classes

    doLast {
        def customMinecraftDir = project.findProperty('minecraftDir') ?: 'F:/QQDownload'
        def modsDir = new File(customMinecraftDir, 'mods')

        // 创建mods目录
        if (!modsDir.exists()) {
            modsDir.mkdirs()
            println "📁 创建mods目录: ${modsDir.absolutePath}"
        }

        // 清理旧的LiquidBounce文件
        def cleaned = 0
        modsDir.listFiles()?.findAll {
            it.name.toLowerCase().contains('liquidbounce') && it.name.endsWith('.jar')
        }?.each {
            println "🗑️ 清理旧文件: ${it.name}"
            it.delete()
            cleaned++
        }

        if (cleaned > 0) {
            println "✅ 清理了 ${cleaned} 个旧的mod文件"
        }

        println "🚀 IDEA启动准备完成 - LiquidBounce Ver_zhzh Edition"
        println "📍 Minecraft目录: ${customMinecraftDir}"
        println "💡 提示: 在开发模式下，mod将从源码直接加载"
    }
}
