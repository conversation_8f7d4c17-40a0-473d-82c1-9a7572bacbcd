# LiquidBounce Ver_zhzh 故障排除指南

## 🚨 当前问题：Java版本冲突

### 问题描述
- 错误：`Unsupported class file major version 65`
- 原因：Gradle使用Java 21运行，但项目需要Java 8
- 影响：无法正常构建和运行项目

### 🎯 推荐解决方案：使用IDEA直接运行

#### 步骤1：在IDEA中运行
1. 打开IDEA
2. 点击右上角的"Start client"运行配置
3. 直接点击运行按钮（绿色三角形）

#### 步骤2：如果IDEA运行失败
1. 检查项目SDK设置：
   - File → Project Structure → Project
   - 确保Project SDK设置为Java 8
   - 确保Project language level设置为8

2. 检查模块设置：
   - File → Project Structure → Modules
   - 确保Language level为8

#### 步骤3：验证运行效果
- 游戏窗口标题应显示：`LiquidBounce Legacy ... - Ver_zhzh Edition`
- 欢迎弹窗应显示：`LiquidBounce Ver_zhzh`

### 🔧 命令行解决方案（高级用户）

#### 方案A：设置正确的Java版本
```batch
# 设置Java 8环境变量
set JAVA_HOME=F:\JDK8
set PATH=%JAVA_HOME%\bin;%PATH%

# 验证版本
java -version
javac -version

# 重新尝试构建
gradlew clean build
```

#### 方案B：使用特定Java版本运行Gradle
```batch
# 使用Java 8运行Gradle
F:\JDK8\bin\java -jar gradle\wrapper\gradle-wrapper.jar clean build
```

### 📋 环境检查清单

#### Java环境
- [ ] Java 8已安装：`F:\JDK8`
- [ ] JAVA_HOME指向Java 8
- [ ] PATH包含Java 8的bin目录
- [ ] `java -version`显示1.8.x

#### IDEA设置
- [ ] Project SDK设置为Java 8
- [ ] Module language level设置为8
- [ ] Gradle JVM设置为Java 8

#### 项目文件
- [ ] `gradle.properties`中的版本设置正确
- [ ] `build.gradle`依赖版本兼容
- [ ] IDEA运行配置正确

### 🎮 测试验证

#### 成功标志
1. **窗口标题**：`LiquidBounce Legacy unknown unknown (Unsupported) | 1.8.9 - Ver_zhzh Edition`
2. **欢迎弹窗**：显示`LiquidBounce Ver_zhzh`
3. **无错误日志**：没有ClassNotFoundException或ASM错误

#### 如果仍有问题
1. 检查游戏日志中的错误信息
2. 确认Minecraft版本为1.8.9
3. 确认Forge版本兼容
4. 检查mods目录中是否有冲突的mod

### 💡 开发建议

#### 推荐工作流
1. **开发**：在IDEA中修改代码
2. **测试**：直接在IDEA中运行"Start client"
3. **调试**：使用IDEA的调试功能
4. **发布**：解决Java环境问题后使用Gradle构建

#### 避免的操作
- 不要混用不同版本的Java
- 不要在Java版本冲突时强制构建
- 不要忽略ASM版本兼容性警告

### 🆘 紧急联系

如果以上方案都无法解决问题：
1. 提供完整的错误日志
2. 提供Java版本信息：`java -version`
3. 提供IDEA版本和设置截图
4. 描述具体的操作步骤和错误现象

---

**记住**：开发阶段最重要的是能够运行和测试代码，构建问题可以后续解决！
