@echo off
chcp 65001 >nul
echo ========================================
echo    LiquidBounce Ver_zhzh 自动构建测试
echo ========================================
echo.

:: 设置变量
set "MINECRAFT_DIR=F:\QQDownload\minecraft"
set "MODS_DIR=%MINECRAFT_DIR%\mods"
set "PROJECT_DIR=%~dp0"

echo [1/4] 清理旧的构建文件...
call gradlew clean
if %ERRORLEVEL% neq 0 (
    echo ❌ 清理失败！
    pause
    exit /b 1
)

echo.
echo [2/4] 开始构建项目...
call gradlew build
if %ERRORLEVEL% neq 0 (
    echo ❌ 构建失败！请检查代码错误
    pause
    exit /b 1
)

echo.
echo [3/4] 部署到Minecraft mods文件夹...

:: 检查mods目录是否存在
if not exist "%MODS_DIR%" (
    echo 📁 创建mods目录: %MODS_DIR%
    mkdir "%MODS_DIR%"
)

:: 删除旧的LiquidBounce mod文件
echo 🗑️ 清理旧的mod文件...
for %%f in ("%MODS_DIR%\*liquidbounce*.jar") do (
    echo    删除: %%~nxf
    del "%%f"
)

:: 复制新构建的jar文件
echo 📦 复制新的mod文件...
for %%f in ("%PROJECT_DIR%build\libs\*.jar") do (
    if not "%%~nxf"=="*-sources.jar" (
        echo    复制: %%~nxf → %MODS_DIR%
        copy "%%f" "%MODS_DIR%\"
    )
)

echo.
echo [4/4] 启动Minecraft进行测试...
echo 🚀 正在启动Minecraft客户端...
echo.
echo ⚠️  注意事项：
echo    - 请在游戏中检查窗口标题是否显示 "LiquidBounce Legacy ... - Ver_zhzh Edition"
echo    - 检查mod列表中是否显示 "LiquidBounce Ver_zhzh"
echo    - 测试完成后请关闭游戏
echo.

:: 切换到Minecraft目录并启动
cd /d "%MINECRAFT_DIR%"

:: 检查是否有启动器
if exist "minecraft.exe" (
    start minecraft.exe
) else if exist "launcher.exe" (
    start launcher.exe
) else (
    echo ⚠️ 未找到Minecraft启动器，请手动启动Minecraft
    echo 📂 已打开Minecraft目录，请手动启动游戏
    start explorer "%MINECRAFT_DIR%"
)

echo.
echo ✅ 构建和部署完成！
echo 📍 Minecraft目录: %MINECRAFT_DIR%
echo 📍 Mods目录: %MODS_DIR%
echo.
echo 按任意键返回项目目录...
pause >nul

:: 返回项目目录
cd /d "%PROJECT_DIR%"
