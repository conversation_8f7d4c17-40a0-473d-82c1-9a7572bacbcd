plugins {
    id "java"
    id "idea"
    id "net.minecraftforge.gradle.forge"
    id "org.spongepowered.mixin"
}

repositories {
    mavenLocal()
    mavenCentral()
    maven { url = "https://repo.spongepowered.org/repository/maven-public/" }
}

archivesBaseName = project.archives_base_name
version = project.mod_version
group = project.maven_group

sourceCompatibility = targetCompatibility = 1.8
compileJava.options.encoding = "UTF-8"

minecraft {
    version = "1.8.9-11.15.1.2318-1.8.9"
    runDir = "run"
    mappings = "stable_22"
    makeObfSourceJar = false
    clientJvmArgs += ["-Dfml.coreMods.load=net.ccbluex.liquidbounce.injection.forge.MixinLoader", "-Xmx4096m", "-Xms1024m", "-Ddev-mode"]
}

configurations {
    runtimeOnly.canBeResolved = true
}

dependencies {
    implementation("org.spongepowered:mixin:0.7.11-SNAPSHOT") {
        transitive = false
        exclude module: "guava"
        exclude module: "commons-io"
        exclude module: "gson"
        exclude module: "launchwrapper"
        exclude module: "log4j-core"
        exclude module: "slf4j-api"
    }

    annotationProcessor("org.spongepowered:mixin:0.7.11-SNAPSHOT")

    implementation "com.jagrosh:DiscordIPC:0.4"

    implementation("com.github.CCBlueX:Elixir:1.2.6") {
        exclude module: "kotlin-stdlib"
        exclude module: "authlib"
    }

    implementation 'com.github.TheAltening:TheAltening4j:d0771f42d3'
    implementation 'com.github.TheAltening:API-Java-AuthLib:63a9702615'

    // 简化的HTTP客户端
    implementation("com.squareup.okhttp3:okhttp:3.14.9")

    // 简化的UI库
    implementation "com.formdev:flatlaf:1.6.5"

    implementation fileTree(include: ["*.jar"], dir: "libs")
}

jar {
    duplicatesStrategy DuplicatesStrategy.EXCLUDE
    
    exclude "LICENSE.txt"
    exclude "META-INF/maven/**"
    exclude "META-INF/versions/**"
    exclude "org/apache/log4j/**"
    exclude "org/apache/commons/**"
    exclude "org/junit/**"
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    from(sourceSets.main.resources.srcDirs) {
        include "mcmod.info"
        expand "version": project.version, "mcversion": project.minecraft.version
    }

    from(sourceSets.main.resources.srcDirs) {
        exclude "mcmod.info"
    }
}

mixin {
    add sourceSets.main, "liquidbounce.mixins.refmap.json"
}

task copyZipInclude(type: Copy) {
    from "src/main/resources/instructions.html"
    into "build/libs"
}

build.dependsOn copyZipInclude

// 简化的部署任务
task deployToCustomMods {
    description = 'Deploy built jar to custom Minecraft mods folder'
    group = 'deployment'
    
    dependsOn build
    
    doLast {
        def customMinecraftDir = project.findProperty('minecraftDir') ?: 'F:/QQDownload'
        def modsDir = new File(customMinecraftDir, 'mods')
        
        if (!modsDir.exists()) {
            modsDir.mkdirs()
            println "📁 创建目录: ${modsDir.absolutePath}"
        }
        
        modsDir.listFiles()?.findAll { 
            it.name.toLowerCase().contains('liquidbounce') && it.name.endsWith('.jar')
        }?.each { 
            println "🗑️ 删除旧文件: ${it.name}"
            it.delete() 
        }
        
        new File('build/libs').listFiles()?.findAll { 
            it.name.endsWith('.jar') && 
            !it.name.contains('-sources') && 
            !it.name.contains('-dev')
        }?.each { jarFile ->
            def targetFile = new File(modsDir, jarFile.name)
            jarFile.withInputStream { input ->
                targetFile.withOutputStream { output ->
                    output << input
                }
            }
            println "📦 复制: ${jarFile.name} → ${modsDir.absolutePath}"
        }
        
        println "✅ 部署完成！"
        println "📍 目标目录: ${modsDir.absolutePath}"
    }
}

// IDEA启动前的快速部署任务
task ideaDeploy {
    description = 'Quick deploy for IDEA run configuration'
    group = 'ide'
    
    dependsOn classes
    
    doLast {
        def customMinecraftDir = project.findProperty('minecraftDir') ?: 'F:/QQDownload'
        def modsDir = new File(customMinecraftDir, 'mods')
        
        if (!modsDir.exists()) {
            modsDir.mkdirs()
            println "📁 创建mods目录: ${modsDir.absolutePath}"
        }
        
        def cleaned = 0
        modsDir.listFiles()?.findAll { 
            it.name.toLowerCase().contains('liquidbounce') && it.name.endsWith('.jar')
        }?.each { 
            println "🗑️ 清理旧文件: ${it.name}"
            it.delete()
            cleaned++
        }
        
        if (cleaned > 0) {
            println "✅ 清理了 ${cleaned} 个旧的mod文件"
        }
        
        println "🚀 IDEA启动准备完成 - LiquidBounce Ver_zhzh Edition"
        println "📍 Minecraft目录: ${customMinecraftDir}"
        println "💡 提示: 在开发模式下，mod将从源码直接加载"
    }
}
