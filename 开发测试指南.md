# LiquidBounce Ver_zhzh 开发测试指南

## 🚀 一键构建测试方案

为了提高开发效率，我们提供了多种自动化构建和测试方案：

### 方案1：快速测试（推荐）
```bash
# 双击运行或在命令行执行
quick-test.bat
```
- ✅ 最简单快捷
- ✅ 自动构建、部署、提示测试
- ✅ 适合日常开发测试

### 方案2：完整自动化
```bash
# 双击运行或在命令行执行
build-and-test.bat
```
- ✅ 完整的构建流程
- ✅ 自动清理旧文件
- ✅ 自动启动Minecraft（如果找到启动器）
- ✅ 详细的步骤提示

### 方案3：PowerShell高级版
```powershell
# 在PowerShell中执行
.\build-and-test.ps1

# 带参数的高级用法
.\build-and-test.ps1 -MinecraftDir "D:\Games\Minecraft"  # 自定义目录
.\build-and-test.ps1 -SkipBuild                          # 跳过构建，仅部署
.\build-and-test.ps1 -SkipLaunch                         # 跳过启动游戏
.\build-and-test.ps1 -CleanOnly                          # 仅清理构建文件
```
- ✅ 功能最强大
- ✅ 支持自定义参数
- ✅ 彩色输出和详细日志
- ✅ 错误处理更完善

### 方案4：Gradle任务
```bash
# 仅构建和部署
gradlew deployToCustomMods -PminecraftDir="F:/QQDownload/minecraft"

# 部署到默认.minecraft目录
gradlew deployToMods
```
- ✅ 集成到构建系统
- ✅ 可以在IDEA中直接运行
- ✅ 支持自定义Minecraft目录

## 📁 目录配置

当前配置的Minecraft目录：`F:\QQDownload\minecraft`

如需修改，可以：
1. 编辑脚本文件中的路径
2. 使用PowerShell版本的 `-MinecraftDir` 参数
3. 使用Gradle任务的 `-PminecraftDir` 参数

## 🎯 测试检查项

运行测试后，请检查以下项目：

### 窗口标题
- ✅ 应显示：`LiquidBounce Legacy unknown unknown (Unsupported) | 1.8.9 - Ver_zhzh Edition`

### Mod列表
- ✅ 在Minecraft mod列表中应显示：`LiquidBounce Ver_zhzh`

### 游戏内界面
- ✅ 欢迎弹窗应显示：`Thank you for downloading and installing LiquidBounce Ver_zhzh!`
- ✅ 各种UI界面应正确显示Ver_zhzh标识

## 🛠️ 开发工作流

推荐的开发工作流程：

1. **修改代码** - 在IDEA中进行开发
2. **快速测试** - 运行 `quick-test.bat`
3. **游戏测试** - 启动Minecraft验证修改效果
4. **提交代码** - 确认无误后提交到git

## 🔧 故障排除

### 构建失败
- 检查Java版本（需要Java 8）
- 检查网络连接（下载依赖）
- 查看错误日志定位问题

### 部署失败
- 检查Minecraft目录路径是否正确
- 确保有写入权限
- 检查磁盘空间

### 游戏启动问题
- 确保Forge已正确安装
- 检查Minecraft版本（需要1.8.9）
- 查看游戏日志排查mod冲突

## 📝 自定义配置

如需修改默认配置，编辑对应脚本文件：

- `quick-test.bat` - 修改第7行的Minecraft目录
- `build-and-test.bat` - 修改第8行的MINECRAFT_DIR变量
- `build-and-test.ps1` - 修改第4行的MinecraftDir参数默认值

## 🎉 享受开发！

现在你可以专注于代码开发，让自动化脚本处理繁琐的构建和部署工作！
